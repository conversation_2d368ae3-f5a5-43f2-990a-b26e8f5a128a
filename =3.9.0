Collecting pytest
  Downloading pytest-8.3.5-py3-none-any.whl.metadata (7.6 kB)
Collecting pytest-cov
  Downloading pytest_cov-6.1.1-py3-none-any.whl.metadata (28 kB)
Collecting pytest-asyncio
  Downloading pytest_asyncio-1.0.0-py3-none-any.whl.metadata (4.0 kB)
Collecting pytest-xdist
  Downloading pytest_xdist-3.7.0-py3-none-any.whl.metadata (3.0 kB)
Collecting aiohttp
  Downloading aiohttp-3.12.6-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.6 kB)
Collecting multidict
  Downloading multidict-6.4.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.3 kB)
Collecting yarl
  Downloading yarl-1.20.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (72 kB)
Collecting iniconfig (from pytest)
  Downloading iniconfig-2.1.0-py3-none-any.whl.metadata (2.7 kB)
Collecting packaging (from pytest)
  Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting pluggy<2,>=1.5 (from pytest)
  Downloading pluggy-1.6.0-py3-none-any.whl.metadata (4.8 kB)
Collecting coverage>=7.5 (from coverage[toml]>=7.5->pytest-cov)
  Downloading coverage-7.8.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.9 kB)
Collecting execnet>=2.1 (from pytest-xdist)
  Downloading execnet-2.1.1-py3-none-any.whl.metadata (2.9 kB)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp)
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp)
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp)
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp)
  Downloading frozenlist-1.6.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (16 kB)
Collecting propcache>=0.2.0 (from aiohttp)
  Downloading propcache-0.3.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting idna>=2.0 (from yarl)
  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
Downloading pytest-8.3.5-py3-none-any.whl (343 kB)
Downloading pluggy-1.6.0-py3-none-any.whl (20 kB)
Downloading pytest_cov-6.1.1-py3-none-any.whl (23 kB)
Downloading pytest_asyncio-1.0.0-py3-none-any.whl (15 kB)
Downloading pytest_xdist-3.7.0-py3-none-any.whl (46 kB)
Downloading aiohttp-3.12.6-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 20.3 MB/s eta 0:00:00
Downloading multidict-6.4.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (223 kB)
Downloading yarl-1.20.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (349 kB)
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading coverage-7.8.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (245 kB)
Downloading execnet-2.1.1-py3-none-any.whl (40 kB)
Downloading frozenlist-1.6.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (316 kB)
Downloading idna-3.10-py3-none-any.whl (70 kB)
Downloading propcache-0.3.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (245 kB)
Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
Downloading packaging-25.0-py3-none-any.whl (66 kB)
Installing collected packages: propcache, pluggy, packaging, multidict, iniconfig, idna, frozenlist, execnet, coverage, attrs, aiohappyeyeballs, yarl, pytest, aiosignal, pytest-xdist, pytest-cov, pytest-asyncio, aiohttp

Successfully installed aiohappyeyeballs-2.6.1 aiohttp-3.12.6 aiosignal-1.3.2 attrs-25.3.0 coverage-7.8.2 execnet-2.1.1 frozenlist-1.6.0 idna-3.10 iniconfig-2.1.0 multidict-6.4.4 packaging-25.0 pluggy-1.6.0 propcache-0.3.1 pytest-8.3.5 pytest-asyncio-1.0.0 pytest-cov-6.1.1 pytest-xdist-3.7.0 yarl-1.20.0
ading typing_extensions-4.13.2-py3-none-any.whl (45 kB)
Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
Downloading packaging-25.0-py3-none-any.whl (66 kB)
Installing collected packages: typing-extensions, tomli, propcache, pluggy, packaging, iniconfig, idna, frozenlist, execnet, coverage, attrs, async-timeout, aiohappyeyeballs, multidict, exceptiongroup, aiosignal, yarl, pytest, pytest-xdist, pytest-cov, pytest-asyncio, aiohttp

Successfully installed aiohappyeyeballs-2.6.1 aiohttp-3.12.6 aiosignal-1.3.2 async-timeout-5.0.1 attrs-25.3.0 coverage-7.8.2 exceptiongroup-1.3.0 execnet-2.1.1 frozenlist-1.6.0 idna-3.10 iniconfig-2.1.0 multidict-6.4.4 packaging-25.0 pluggy-1.6.0 propcache-0.3.1 pytest-8.3.5 pytest-asyncio-1.0.0 pytest-cov-6.1.1 pytest-xdist-3.7.0 tomli-2.2.1 typing-extensions-4.13.2 yarl-1.20.0
